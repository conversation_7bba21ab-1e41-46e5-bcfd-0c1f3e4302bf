"use client"

import React from 'react';
import { useUser } from '@/hooks/use-user';
import { useTheme } from '@/components/providers/theme-provider';
import Header from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Settings as SettingsIcon, 
  Moon, 
  Sun, 
  Bell, 
  Shield, 
  Smartphone,
  Globe,
  Lock,
  Eye,
  Mail
} from 'lucide-react';

export default function SettingsPage() {
  const { user } = useUser();
  const { theme, toggleTheme } = useTheme();
  
  // Mock settings state - in a real app, these would come from user preferences
  const [notifications, setNotifications] = React.useState({
    email: true,
    push: false,
    sms: false,
    shifts: true,
    jobs: true,
    updates: false
  });

  const [privacy, setPrivacy] = React.useState({
    profileVisible: true,
    showEmail: false,
    showPhone: false
  });

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-12">
              <p className="text-gray-400">Please log in to access settings.</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <Header />
      
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Settings Header */}
          <div className="flex items-center gap-3 mb-6">
            <SettingsIcon className="h-8 w-8 text-indigo-400" />
            <div>
              <h1 className="text-3xl font-bold text-white">Settings</h1>
              <p className="text-gray-400">Manage your account preferences and privacy settings</p>
            </div>
          </div>

          {/* Appearance Settings */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                {theme === 'dark' ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />}
                Appearance
              </CardTitle>
              <CardDescription className="text-gray-400">
                Customize how the application looks and feels
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-gray-300">Theme</Label>
                  <p className="text-sm text-gray-400">
                    Choose between light and dark mode
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleTheme}
                  className="border-gray-600 hover:bg-gray-700"
                >
                  {theme === 'dark' ? (
                    <>
                      <Sun className="h-4 w-4 mr-2" />
                      Light Mode
                    </>
                  ) : (
                    <>
                      <Moon className="h-4 w-4 mr-2" />
                      Dark Mode
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notifications
              </CardTitle>
              <CardDescription className="text-gray-400">
                Configure how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-gray-300 flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email Notifications
                    </Label>
                    <p className="text-sm text-gray-400">
                      Receive notifications via email
                    </p>
                  </div>
                  <Switch
                    checked={notifications.email}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, email: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-gray-300 flex items-center gap-2">
                      <Smartphone className="h-4 w-4" />
                      Push Notifications
                    </Label>
                    <p className="text-sm text-gray-400">
                      Receive push notifications on your device
                    </p>
                  </div>
                  <Switch
                    checked={notifications.push}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, push: checked }))
                    }
                  />
                </div>
              </div>
              
              <Separator className="bg-gray-700" />
              
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-300">Notification Types</h4>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-gray-300">Shift Updates</Label>
                    <p className="text-sm text-gray-400">
                      Get notified about shift changes and assignments
                    </p>
                  </div>
                  <Switch
                    checked={notifications.shifts}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, shifts: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-gray-300">Job Updates</Label>
                    <p className="text-sm text-gray-400">
                      Get notified about new jobs and updates
                    </p>
                  </div>
                  <Switch
                    checked={notifications.jobs}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, jobs: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-gray-300">System Updates</Label>
                    <p className="text-sm text-gray-400">
                      Get notified about system maintenance and updates
                    </p>
                  </div>
                  <Switch
                    checked={notifications.updates}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, updates: checked }))
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Privacy Settings */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Privacy & Security
              </CardTitle>
              <CardDescription className="text-gray-400">
                Control your privacy and security preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-gray-300 flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Profile Visibility
                  </Label>
                  <p className="text-sm text-gray-400">
                    Make your profile visible to other team members
                  </p>
                </div>
                <Switch
                  checked={privacy.profileVisible}
                  onCheckedChange={(checked) => 
                    setPrivacy(prev => ({ ...prev, profileVisible: checked }))
                  }
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-gray-300">Show Email Address</Label>
                  <p className="text-sm text-gray-400">
                    Display your email address on your profile
                  </p>
                </div>
                <Switch
                  checked={privacy.showEmail}
                  onCheckedChange={(checked) => 
                    setPrivacy(prev => ({ ...prev, showEmail: checked }))
                  }
                />
              </div>
              
              <Separator className="bg-gray-700" />
              
              <div className="space-y-3">
                <Button variant="outline" className="w-full border-gray-600 hover:bg-gray-700">
                  <Lock className="h-4 w-4 mr-2" />
                  Change Password
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Save Settings */}
          <div className="flex justify-end gap-3">
            <Button variant="outline">
              Reset to Defaults
            </Button>
            <Button>
              Save All Settings
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
}
