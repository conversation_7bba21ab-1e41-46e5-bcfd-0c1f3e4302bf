'use client';

import { useUser } from '@/hooks/use-user';
import { useApi } from '@/hooks/use-api';
import { useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { PlusIcon, SparklesIcon } from '@/components/IconComponents';
import { EnhancedStatusBadge } from '@/components/ui/enhanced-status-badge';
import { EnhancedCard, MetricCard, StatusCard } from '@/components/ui/enhanced-card';
import {
  Briefcase,
  Calendar,
  Users,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Building,
  MapPin
} from 'lucide-react';

type Job = {
  id: string;
  name: string;
  description: string;
  status: 'Pending' | 'Active' | 'Completed' | 'On Hold';
  companyId: string;
  company: {
    id: string;
    name: string;
  };
  requestedWorkers: number;
  shifts: {
    id: string;
    status: 'Upcoming' | 'In Progress' | 'Completed';
  }[];
};

type Shift = {
  id: string;
  jobName: string;
  companyName: string;
  date: string;
  startTime: string;
  endTime: string;
  status: string;
};

export default function ManagerDashboard() {
  const { user } = useUser();
  const router = useRouter();

  const { data: shiftsData, loading: shiftsLoading, error: shiftsError } = useApi<{ shifts: Shift[] }>('/api/shifts?filter=active');
  const { data: jobsData, loading: jobsLoading, error: jobsError } = useApi<{ jobs: Job[] }>('/api/jobs');

  const handleJobClick = (job: Job) => {
    router.push(`/jobs/${job.id}`);
  };

  const handleShiftClick = (shift: Shift) => {
    router.push(`/shifts/${shift.id}`);
  };

  const shifts = shiftsData?.shifts || [];
  const jobs = jobsData?.jobs || [];

  // Simple categorization for dashboard overview
  const categorizedShifts = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todaysShifts = shifts.filter(shift => {
      const shiftDate = new Date(shift.date);
      shiftDate.setHours(0, 0, 0, 0);
      return shiftDate.getTime() === today.getTime();
    });

    return { todaysShifts };
  }, [shifts]);

  if (shiftsLoading || jobsLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center py-8 text-gray-400">Loading dashboard data...</div>
          </div>
        </main>
      </div>
    );
  }

  if (shiftsError || jobsError) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-red-500">Error loading dashboard data.</div>
          </div>
        </main>
      </div>
    );
  }

  // Dashboard overview content - users can navigate to specific sections via NavigationBar
  const renderDashboardOverview = () => {
    // Calculate metrics
    const totalJobs = jobs.length;
    const activeJobs = jobs.filter(job => job.status === 'Active').length;
    const completedJobs = jobs.filter(job => job.status === 'Completed').length;
    const todayShifts = categorizedShifts.todaysShifts.length;

    return (
      <div className="space-y-8">
        {/* Metrics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Total Jobs"
            value={totalJobs}
            icon={Briefcase}
            iconColor="text-indigo-500"
            onClick={() => router.push('/jobs')}
            className="hover:shadow-indigo-500/20"
          />
          <MetricCard
            title="Active Jobs"
            value={activeJobs}
            change={activeJobs > 0 ? `${Math.round((activeJobs/totalJobs) * 100)}%` : '0%'}
            changeType="positive"
            icon={TrendingUp}
            iconColor="text-emerald-500"
            onClick={() => router.push('/jobs')}
            className="hover:shadow-emerald-500/20"
          />
          <MetricCard
            title="Today's Shifts"
            value={todayShifts}
            icon={Calendar}
            iconColor="text-blue-500"
            onClick={() => router.push('/shifts')}
            className="hover:shadow-blue-500/20"
          />
          <MetricCard
            title="Completed Jobs"
            value={completedJobs}
            icon={CheckCircle}
            iconColor="text-purple-500"
            onClick={() => router.push('/jobs')}
            className="hover:shadow-purple-500/20"
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Jobs Overview */}
          <EnhancedCard
            variant="gradient"
            icon={Briefcase}
            iconColor="text-indigo-500"
            title="Recent Jobs"
            className="hover:shadow-xl hover:shadow-indigo-500/10"
          >
            <div className="space-y-4">
              <div className="flex justify-end">
                <button
                  onClick={() => router.push('/jobs')}
                  className="text-indigo-400 hover:text-indigo-300 text-sm font-medium transition-colors"
                >
                  View All →
                </button>
              </div>

              {jobs.length > 0 ? (
                <div className="space-y-3">
                  {jobs.slice(0, 3).map(job => (
                    <StatusCard
                      key={job.id}
                      title={job.name}
                      description={job.company?.name || 'No Client'}
                      status={
                        <EnhancedStatusBadge
                          status={job.status.toLowerCase().replace(' ', '-') as any}
                          size="sm"
                          variant="soft"
                        />
                      }
                      icon={Building}
                      iconColor="text-gray-500"
                      onClick={() => handleJobClick(job)}
                      className="hover:shadow-md transition-all duration-200"
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <SparklesIcon className="mx-auto h-12 w-12 text-indigo-400 mb-3" />
                  <p className="text-gray-400 dark:text-gray-500">No jobs found</p>
                </div>
              )}
            </div>
          </EnhancedCard>

          {/* Today's Shifts Overview */}
          <EnhancedCard
            variant="gradient"
            icon={Calendar}
            iconColor="text-blue-500"
            title="Today's Shifts"
            className="hover:shadow-xl hover:shadow-blue-500/10"
          >
            <div className="space-y-4">
              <div className="flex justify-end">
                <button
                  onClick={() => router.push('/shifts')}
                  className="text-blue-400 hover:text-blue-300 text-sm font-medium transition-colors"
                >
                  View All →
                </button>
              </div>

              {categorizedShifts.todaysShifts.length > 0 ? (
                <div className="space-y-3">
                  {categorizedShifts.todaysShifts.slice(0, 3).map(shift => (
                    <StatusCard
                      key={shift.id}
                      title={shift.jobName}
                      description={
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{shift.startTime} - {shift.endTime}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span>{shift.companyName}</span>
                          </div>
                        </div>
                      }
                      status={
                        <EnhancedStatusBadge
                          status={shift.status.toLowerCase().replace(' ', '-') as any}
                          size="sm"
                          variant="soft"
                        />
                      }
                      icon={Calendar}
                      iconColor="text-blue-500"
                      onClick={() => handleShiftClick(shift)}
                      className="hover:shadow-md transition-all duration-200"
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-blue-400 mb-3" />
                  <p className="text-gray-400 dark:text-gray-500">No shifts today</p>
                </div>
              )}
            </div>
          </EnhancedCard>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 font-sans">
      <Header>
        <button className="sample-btn-primary">
            <PlusIcon />
            <span>Create Job<span className="hidden sm:inline"> or Shift</span></span>
        </button>
      </Header>
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
            {/* Manager Dashboard Content */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">Manager Dashboard</h1>
              <p className="text-gray-400">Overview of jobs, shifts, and team management</p>
            </div>

            {/* Dashboard Overview - Navigation handled by NavigationBar in Header */}
            <div className="mt-6">
                {renderDashboardOverview()}
            </div>
        </div>
      </main>
    </div>
  );
}
