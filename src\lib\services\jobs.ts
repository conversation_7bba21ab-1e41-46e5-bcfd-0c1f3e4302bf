import { prisma } from '../prisma';
import { Job, Prisma, UserRole } from '@prisma/client';
import { User } from 'next-auth';
import { hasAnyRole } from '../auth';

// Define a type for the job with its relations
export type JobWithCompany = Prisma.JobGetPayload<{
  include: {
    company: {
      select: {
        name: true;
      };
    };
  };
}>;

export async function getAllJobs(user: User): Promise<JobWithCompany[]> {
  if (!hasAnyRole(user, [UserRole.Admin, UserRole.Staff, UserRole.CrewChief])) {
    throw new Error('Not authorized to view all jobs');
  }
  return prisma.job.findMany({
    include: {
      company: {
        select: {
          name: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
}

export async function getJobById(
  user: User,
  id: string
): Promise<JobWithCompany | null> {
  const job = await prisma.job.findUnique({
    where: { id },
    include: {
      company: {
        select: {
          name: true,
        },
      },
    },
  });

  if (!job) return null;

  if (
    !hasAnyRole(user, [UserRole.Admin]) &&
    user.companyId !== job.companyId
  ) {
    throw new Error('Not authorized to view this job');
  }

  return job;
}

export async function getJobsByCompanyId(id: string): Promise<JobWithCompany[]> {
  return prisma.job.findMany({
    where: { companyId: id },
    include: {
      company: {
        select: {
          name: true,
        },
      },
    },
    orderBy: { createdAt: 'desc' },
  });
}



export async function updateJob(
  user: User,
  id: string,
  data: {
    name?: string;
    description?: string | null;
    companyId?: string;
    status?: string;
  }
): Promise<JobWithCompany> {
  const job = await prisma.job.findUnique({ where: { id } });
  if (!job) throw new Error('Job not found');

  if (
    !hasAnyRole(user, [UserRole.Admin]) &&
    user.companyId !== job.companyId
  ) {
    throw new Error('Not authorized to update this job');
  }

  return prisma.job.update({
    where: { id },
    data,
    include: {
      company: {
        select: {
          name: true,
        },
      },
    },
  });
}

export async function deleteJob(user: User, id: string): Promise<void> {
  const job = await prisma.job.findUnique({ where: { id } });
  if (!job) throw new Error('Job not found');

  if (
    !hasAnyRole(user, [UserRole.Admin]) &&
    user.companyId !== job.companyId
  ) {
    throw new Error('Not authorized to delete this job');
  }

  await prisma.job.delete({
    where: { id },
  });
}

export async function createJob(
  user: User,
  data: {
    name: string;
    description?: string;
    companyId: string;
    startDate?: Date;
  }
): Promise<Job> {
  if (
    !hasAnyRole(user, [UserRole.Admin]) &&
    user.companyId !== data.companyId
  ) {
    throw new Error('Not authorized to create a job for this company');
  }
  return prisma.job.create({
    data: {
      name: data.name,
      description: data.description,
      companyId: data.companyId,
      startDate: data.startDate,
    },
  });
}
