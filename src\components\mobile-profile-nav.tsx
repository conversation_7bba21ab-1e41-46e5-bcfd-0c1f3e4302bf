"use client"

import React from 'react';
import Link from 'next/link';
import { useUser } from "@/hooks/use-user";
import { useTheme } from "./providers/theme-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  LogOut, 
  User as UserIcon, 
  Sun, 
  Moon, 
  Settings,
  ChevronDown,
  Menu
} from "lucide-react";

interface MobileProfileNavProps {
  className?: string;
}

export function MobileProfileNav({ className = "" }: MobileProfileNavProps) {
  const { user, logout } = useUser();
  const { theme, toggleTheme } = useTheme();

  if (!user) {
    return null;
  }

  const handleLogout = async () => {
    await logout();
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Theme Toggle - Mobile First */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleTheme}
        className="h-9 w-9 p-0 hover:bg-gray-700/50 text-gray-300 hover:text-white"
        aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
      >
        {theme === 'dark' ? (
          <Sun className="h-4 w-4" />
        ) : (
          <Moon className="h-4 w-4" />
        )}
      </Button>

      {/* Profile Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className="h-9 px-2 hover:bg-gray-700/50 focus:bg-gray-700/50 data-[state=open]:bg-gray-700/50"
            aria-label="User menu"
          >
            <div className="flex items-center gap-2">
              <Avatar className="h-7 w-7 ring-2 ring-gray-600">
                <AvatarImage 
                  src={user.avatarUrl || user.avatar} 
                  alt={user.name || 'User'} 
                />
                <AvatarFallback className="bg-indigo-600 text-white text-xs font-medium">
                  {getInitials(user.name || user.email || 'U')}
                </AvatarFallback>
              </Avatar>
              
              {/* Show name on larger screens */}
              <div className="hidden sm:flex sm:flex-col sm:items-start sm:min-w-0">
                <span className="text-sm font-medium text-white truncate max-w-24 lg:max-w-32">
                  {user.name || 'User'}
                </span>
                <span className="text-xs text-gray-400 truncate max-w-24 lg:max-w-32">
                  {user.role || 'Staff'}
                </span>
              </div>
              
              <ChevronDown className="h-3 w-3 text-gray-400 hidden sm:block" />
            </div>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent 
          className="w-64 bg-gray-800 border-gray-700" 
          align="end" 
          forceMount
          sideOffset={8}
        >
          <DropdownMenuLabel className="font-normal p-3">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none text-white">
                {user.name || 'User'}
              </p>
              <p className="text-xs leading-none text-gray-400">
                {user.email}
              </p>
              <p className="text-xs leading-none text-gray-500">
                {user.role || 'Staff'}
              </p>
            </div>
          </DropdownMenuLabel>
          
          <DropdownMenuSeparator className="bg-gray-700" />
          
          <DropdownMenuItem asChild className="cursor-pointer hover:bg-gray-700 focus:bg-gray-700">
            <Link href="/profile" className="flex items-center w-full">
              <UserIcon className="mr-3 h-4 w-4 text-gray-400" />
              <span className="text-gray-200">Profile</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild className="cursor-pointer hover:bg-gray-700 focus:bg-gray-700">
            <Link href="/settings" className="flex items-center w-full">
              <Settings className="mr-3 h-4 w-4 text-gray-400" />
              <span className="text-gray-200">Settings</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem 
            onClick={toggleTheme}
            className="cursor-pointer hover:bg-gray-700 focus:bg-gray-700"
          >
            {theme === 'dark' ? (
              <Sun className="mr-3 h-4 w-4 text-gray-400" />
            ) : (
              <Moon className="mr-3 h-4 w-4 text-gray-400" />
            )}
            <span className="text-gray-200">
              {theme === 'dark' ? 'Light' : 'Dark'} Mode
            </span>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator className="bg-gray-700" />
          
          <DropdownMenuItem 
            onClick={handleLogout} 
            className="cursor-pointer hover:bg-red-900/20 focus:bg-red-900/20 text-red-400 focus:text-red-400"
          >
            <LogOut className="mr-3 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
