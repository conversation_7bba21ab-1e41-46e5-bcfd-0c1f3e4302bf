# HoliTime VM Management Guide

## VM Details
- **Instance Name**: holitime-vm
- **Zone**: us-west2-a
- **Machine Type**: e2-standard-2
- **External IP**: *************
- **Internal IP**: **********

## Application Details
- **Application Path**: /opt/holitime
- **Process Manager**: PM2
- **Web Server**: <PERSON><PERSON><PERSON> (reverse proxy)
- **Node.js Version**: 20.x
- **Database**: PostgreSQL on Cloud SQL (*************)

## Access URLs
- **Main Application**: http://*************
- **Direct Node.js**: http://*************:3000
- **Health Check**: http://*************/api/health
- **Debug Info**: http://*************/api/debug/db-health

## Common Commands

### SSH into VM
```bash
gcloud compute ssh holitime-vm --zone=us-west2-a
```

### Check Application Status
```bash
gcloud compute ssh holitime-vm --zone=us-west2-a --command="pm2 status"
```

### Restart Application
```bash
gcloud compute ssh holitime-vm --zone=us-west2-a --command="cd /opt/holitime && pm2 restart holitime"
```

### View Application Logs
```bash
gcloud compute ssh holitime-vm --zone=us-west2-a --command="pm2 logs holitime"
```

### Update Application Code
1. Build locally: `npm run build`
2. Transfer files: `gcloud compute scp --recurse .next holitime-vm:/opt/holitime/ --zone=us-west2-a`
3. Restart: `gcloud compute ssh holitime-vm --zone=us-west2-a --command="cd /opt/holitime && pm2 restart holitime"`

### Check Nginx Status
```bash
gcloud compute ssh holitime-vm --zone=us-west2-a --command="sudo systemctl status nginx"
```

### Restart Nginx
```bash
gcloud compute ssh holitime-vm --zone=us-west2-a --command="sudo systemctl restart nginx"
```

## Test Credentials
All users use password: **password123**
- Admin: <EMAIL>
- Manager: <EMAIL>
- Crew Chief: <EMAIL>
- Employees: employee1@example.<NAME_EMAIL>

## Environment Variables
Located at: `/opt/holitime/.env.production`
- NODE_ENV=production
- PORT=3000
- DATABASE_URL=******************************************************/holitime
- NEXTAUTH_URL=http://*************

## Firewall Rules
- allow-holitime-app: tcp:3000 (for direct Node.js access)
- default-allow-http: tcp:80 (for Nginx)
- default-allow-https: tcp:443 (for future SSL)

## Database Connection
- Host: ************* (Cloud SQL)
- Database: holitime
- User: postgres
- Password: AdminPass123!
- Port: 5432

## Monitoring
- PM2 Dashboard: `pm2 monit`
- System Resources: `htop`
- Disk Usage: `df -h`
- Memory Usage: `free -h`
