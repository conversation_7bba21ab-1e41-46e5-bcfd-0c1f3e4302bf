'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

export default function TestAuthPage() {
  console.log('TestAuthPage rendering...');

  const { data: session, status } = useSession();
  const [apiTest, setApiTest] = useState<any>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  console.log('Session status:', status, 'Session:', session);

  useEffect(() => {
    console.log('useEffect triggered, session:', session);
    if (session) {
      console.log('Making API call...');
      // Test API call
      fetch('/api/debug/db-health')
        .then(res => {
          console.log('API response status:', res.status);
          return res.json();
        })
        .then(data => {
          console.log('API data:', data);
          setApiTest(data);
        })
        .catch(err => {
          console.error('API error:', err);
          setApiError(err.message);
        });
    }
  }, [session]);

  if (status === 'loading') {
    return <div>Loading authentication...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Authentication Test</h1>
      
      <div className="mb-4">
        <h2 className="text-lg font-semibold">Session Status:</h2>
        <p>Status: {status}</p>
        <p>User: {session?.user?.name || 'Not authenticated'}</p>
        <p>Email: {session?.user?.email || 'N/A'}</p>
        <p>Role: {(session?.user as any)?.role || 'N/A'}</p>
      </div>

      <div className="mb-4">
        <h2 className="text-lg font-semibold">API Test:</h2>
        {apiTest ? (
          <pre className="bg-gray-100 p-2 rounded text-sm">
            {JSON.stringify(apiTest, null, 2)}
          </pre>
        ) : apiError ? (
          <p className="text-red-500">Error: {apiError}</p>
        ) : (
          <p>No API test performed (not authenticated)</p>
        )}
      </div>

      <div className="mb-4">
        <h2 className="text-lg font-semibold">Actions:</h2>
        {session ? (
          <button 
            onClick={() => window.location.href = '/dashboard'}
            className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
          >
            Go to Dashboard
          </button>
        ) : (
          <button 
            onClick={() => window.location.href = '/login'}
            className="bg-green-500 text-white px-4 py-2 rounded mr-2"
          >
            Login
          </button>
        )}
      </div>
    </div>
  );
}
