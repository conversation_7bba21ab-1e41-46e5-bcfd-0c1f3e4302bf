import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/middleware';
import { getAllJobs, createJob } from '@/lib/services/jobs';
import { UserRole } from '@prisma/client';
import { User } from '@prisma/client';
import { jobValidation } from '@/lib/validation';
import { handleDatabaseError, createErrorResponse } from '@/lib/api-error-handler';
import { connectPrisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Ensure database connection
    await connectPrisma();

    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Allow Admin, Manager, Staff, and CrewChief to view jobs
    if (![UserRole.Admin, UserRole.Manager, UserRole.Staff, UserRole.CrewChief].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const jobs = await getAllJob<PERSON>(user as User);

    return NextResponse.json({
      success: true,
      jobs,
      timestamp: new Date().toISOString(),
      count: jobs.length
    });
  } catch (error) {
    console.error('Error getting jobs:', error);

    // Handle database-specific errors
    const apiError = handleDatabaseError(error);
    return createErrorResponse(apiError);
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Only managers and admins can create jobs
    if (![UserRole.Admin, UserRole.Staff].includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validation = jobValidation.create.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid request body',
          issues: validation.error.flatten().fieldErrors,
        },
        { status: 400 }
      );
    }

    const { name, description, companyId } = validation.data;

    const job = await createJob(user as User, {
      name,
      description,
      companyId,
    });

    if (!job) {
      return NextResponse.json(
        { error: 'Failed to create job' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      job,
    });
  } catch (error) {
    console.error('Error creating job:', error);

    // Handle database-specific errors
    const apiError = handleDatabaseError(error);
    return createErrorResponse(apiError);
  }
}
