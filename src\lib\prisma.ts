import { PrismaClient } from '@prisma/client';

// Create a global variable to store the Prisma client instance
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Enhanced Prisma client configuration for better connectivity
const createPrismaClient = () => {
  const databaseUrl = process.env.DATABASE_URL;

  if (!databaseUrl) {
    throw new Error('DATABASE_URL environment variable is not set');
  }

  // Cloud SQL uses Google's managed SSL certificates, no custom CA needed
  console.log('✅ Using Google Cloud SQL with managed SSL certificates');

  // Add connection pool parameters for production
  const url = new URL(databaseUrl);
  if (process.env.NODE_ENV === 'production') {
    url.searchParams.set('connection_limit', '5');
    url.searchParams.set('pool_timeout', '10');
    url.searchParams.set('connect_timeout', '60');
    url.searchParams.set('socket_timeout', '60');
  }

  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: url.toString(),
      },
    },
  });
};

const prisma = globalForPrisma.prisma ?? createPrismaClient();

// Connection management
let isConnected = false;

// Enhanced connection function with retry logic
export async function connectPrisma() {
  if (isConnected) return;

  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      await prisma.$connect();
      isConnected = true;
      console.log('✅ Prisma connected successfully');
      return;
    } catch (error) {
      retries++;
      console.log(`❌ Prisma connection attempt ${retries} failed:`, error);

      if (retries < maxRetries) {
        const delay = Math.pow(2, retries) * 1000; // Exponential backoff
        console.log(`⏳ Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        throw error;
      }
    }
  }
}

// Graceful disconnect
export async function disconnectPrisma() {
  if (isConnected) {
    await prisma.$disconnect();
    isConnected = false;
    console.log('✅ Prisma disconnected');
  }
}

// In development, store the client on the global object to prevent multiple instances
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

// Handle process termination
process.on('beforeExit', async () => {
  await disconnectPrisma();
});

process.on('SIGINT', async () => {
  await disconnectPrisma();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectPrisma();
  process.exit(0);
});

export { prisma };
