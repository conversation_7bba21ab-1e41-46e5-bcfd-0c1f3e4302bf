"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import Link from "next/link"
import { useUser } from "@/hooks/use-user"
import { useClients } from "@/hooks/use-api"
import { Button, Card, Text, Group, ActionIcon, Badge, Stack, Title } from '@mantine/core'
import { Plus, ExternalLink, Mail, User, Calendar } from "lucide-react"
import Header from '@/components/Header'

function ClientsPage() {
  const { user } = useUser()
  const router = useRouter()
  const canEdit = user?.role === 'Admin'
  const { data: companiesData, loading, error } = useClients()

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">Loading companies...</div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center py-8">
              <div className="text-red-400">Error loading companies: {error.toString()}</div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  const companies = companiesData?.companies || []

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <Header>
        {canEdit && (
          <Button onClick={() => router.push('/clients/new')} className="sample-btn-primary">
            <Plus className="mr-2 h-4 w-4" />
            Add Company
          </Button>
        )}
      </Header>

      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <Stack gap="lg">
      <Group justify="space-between">
        <div>
          <Title order={1}>Companies</Title>
          <Text c="dimmed">
            Manage companies and view their job history
          </Text>
        </div>
        {canEdit && (
          <Button onClick={() => router.push('/clients/new')} leftSection={<Plus size={16} />}>
            Add Company
          </Button>
        )}
      </Group>

      {companies.length > 0 ? (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {companies.map(company => (
            <div
              key={company.id}
              className="mobile-card cursor-pointer hover:shadow-lg transition-shadow duration-200 active:scale-98"
              onClick={() => router.push(`/clients/${company.id}`)}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="font-semibold mobile-text-lg text-gray-900 truncate">
                  {company.name}
                </h3>
              </div>
              
              <div className="space-y-3">
                {company.email && (
                  <div className="flex items-center gap-3">
                    <Mail size={18} className="text-gray-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 truncate">{company.email}</span>
                  </div>
                )}
                {company.phone && (
                  <div className="flex items-center gap-3">
                    <User size={18} className="text-gray-500 flex-shrink-0" />
                    <span className="text-sm text-gray-700 truncate">{company.phone}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Card padding="lg" radius="md" withBorder>
          <Stack align="center" justify="center" style={{ minHeight: '300px' }}>
            <Title order={3}>No companies found</Title>
            <Text c="dimmed">
              Get started by adding your first company.
            </Text>
            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => router.push('/clients/new')}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Company
              </Button>
            )}
          </Stack>
        </Card>
      )}
          </Stack>
        </div>
      </main>
    </div>
  )
}

export default ClientsPage;

