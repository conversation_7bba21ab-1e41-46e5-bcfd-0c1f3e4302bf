@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 17, 24, 39;
  --background-end-rgb: 31, 41, 55;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-shift {
  animation: gradient-shift 15s ease infinite;
}

body {
  color: rgb(var(--foreground-rgb));
  background:
    linear-gradient(
      to bottom right,
      rgb(var(--background-start-rgb)),
      rgb(var(--background-end-rgb))
    ),
    radial-gradient(
      circle at 1px 1px,
      rgba(255,255,255,0.05) 1px,
      transparent 1px
    );
  background-size: 100% 100%, 30px 30px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.2);
  border-radius: 4px;
}

/* Focus styles */
a, button, input, textarea {
  @apply focus-visible:outline focus-visible:outline-2 focus-visible:outline-indigo-500 focus-visible:outline-offset-2;
}

/* Smooth transitions */
* {
  @apply transition-colors duration-200;
}

/* Sample Frontend Design System Classes */
@layer components {
  /* Enhanced Card Components */
  .sample-card {
    @apply bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-xl overflow-hidden transition-all duration-300 hover:shadow-indigo-500/30 hover:ring-2 hover:ring-indigo-400 flex flex-col cursor-pointer hover:-translate-y-2 hover:scale-[1.02] border border-gray-700/50;
  }

  .sample-card-static {
    @apply bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-700/50 backdrop-blur-sm;
  }

  .sample-card-glass {
    @apply bg-gray-800/80 backdrop-blur-md rounded-xl shadow-xl overflow-hidden border border-gray-700/30 transition-all duration-300;
  }

  /* Enhanced Button Styles */
  .sample-btn-primary {
    @apply flex items-center justify-center gap-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-indigo-500/30 active:scale-95;
  }

  .sample-btn-secondary {
    @apply px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300 hover:from-gray-600 hover:to-gray-700 hover:text-white shadow-md hover:shadow-lg;
  }

  .sample-btn-success {
    @apply flex items-center justify-center gap-2 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-500 hover:to-green-500 text-white font-semibold py-2 px-4 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-emerald-500/30;
  }

  .sample-btn-warning {
    @apply flex items-center justify-center gap-2 bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-500 hover:to-orange-500 text-white font-semibold py-2 px-4 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-amber-500/30;
  }

  .sample-btn-danger {
    @apply flex items-center justify-center gap-2 bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-500 hover:to-rose-500 text-white font-semibold py-2 px-4 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 hover:shadow-red-500/30;
  }

  /* Tab Navigation */
  .sample-tab-button {
    @apply px-3 py-2 text-sm sm:text-base font-medium rounded-t-lg transition-colors duration-200 flex items-center gap-2;
  }

  .sample-tab-active {
    @apply border-b-2 border-indigo-500 text-white;
  }

  .sample-tab-inactive {
    @apply border-b-2 border-transparent text-gray-400 hover:text-white;
  }

  /* Status Badges */
  .sample-status-active {
    @apply bg-green-500 text-green-50 ring-1 ring-inset ring-green-600/20;
  }

  .sample-status-completed {
    @apply bg-gray-500 text-gray-100 ring-1 ring-inset ring-gray-500/20;
  }

  .sample-status-pending {
    @apply bg-yellow-500 text-yellow-900 ring-1 ring-inset ring-yellow-600/20;
  }

  .sample-status-in-progress {
    @apply bg-yellow-400/10 text-yellow-300 ring-1 ring-inset ring-yellow-400/30;
  }

  .sample-status-upcoming {
    @apply bg-blue-500/10 text-blue-300 ring-1 ring-inset ring-blue-500/30;
  }

  /* Input Styles */
  .sample-input {
    @apply block w-full bg-gray-700 border border-transparent rounded-md py-2.5 px-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm;
  }

  /* Header Styles */
  .sample-header {
    @apply bg-gray-800/50 backdrop-blur-sm shadow-lg sticky top-0 z-10;
  }

  /* Progress Bar */
  .sample-progress-bar {
    @apply w-full bg-gray-700 rounded-full h-3.5;
  }

  .sample-progress-fill {
    @apply bg-gradient-to-r from-green-400 to-green-600 h-3.5 rounded-full transition-all duration-500;
  }

  /* Empty State */
  .sample-empty-state {
    @apply text-center py-16 mt-8 bg-gray-800/50 rounded-lg border border-gray-700;
  }

  /* Mobile-First Responsive Utilities */
  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  .mobile-flex {
    @apply flex flex-col sm:flex-row items-start sm:items-center gap-4;
  }

  .mobile-text {
    @apply text-sm sm:text-base;
  }

  .mobile-padding {
    @apply p-4 sm:p-6 lg:p-8;
  }

  /* Enhanced Scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(75 85 99) rgb(31 41 55);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(31 41 55);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(75 85 99);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Loading States */
  .loading-shimmer {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Glass Effect */
  .glass-effect {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: rgba(31, 41, 55, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }
}
