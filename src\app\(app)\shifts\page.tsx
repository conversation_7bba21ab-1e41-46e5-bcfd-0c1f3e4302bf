"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { format, isToday, isTomorrow, isYesterday } from "date-fns"
import { useUser } from "@/hooks/use-user"
import { useShiftsByDate } from "@/hooks/use-api"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import Header from '@/components/Header'
import {
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Plus,
  Search,
  Filter,
  AlertCircle,
  RefreshCw,
  Users
} from "lucide-react"

export default function ShiftsPage() {
  const { user } = useUser()
  const router = useRouter()

  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateFilter, setDateFilter] = useState("all")
  const [companyFilter, setCompanyFilter] = useState("all")

  const canManage = user?.role === 'Admin' || user?.role === 'CrewChief'

  const { data, loading, error, refetch } = useShiftsByDate(dateFilter, statusFilter, companyFilter, searchTerm)

  const shifts = data?.shifts || []
  
  useEffect(() => {
    refetch()
  }, [dateFilter, refetch])

  const handleRowClick = (shiftId: string) => {
    router.push(`/shifts/${shiftId}`)
  }

  const getDateBadge = (date: string) => {
    const shiftDate = new Date(date)
    if (isToday(shiftDate)) {
      return <Badge variant="default" className="bg-blue-600 text-white">Today</Badge>
    }
    if (isTomorrow(shiftDate)) {
      return <Badge variant="default" className="bg-green-600 text-white">Tomorrow</Badge>
    }
    if (isYesterday(shiftDate)) {
      return <Badge variant="secondary">Yesterday</Badge>
    }
    return <Badge variant="outline">{format(shiftDate, 'MMM d')}</Badge>
  }

  const getStatusBadge = (status: string) => {
    const statusStyles: Record<string, string> = {
      'Completed': 'bg-green-600 text-white',
      'In Progress': 'bg-blue-600 text-white',
      'Active': 'bg-blue-600 text-white',
      'Upcoming': 'bg-yellow-600 text-white',
      'Pending': 'bg-orange-600 text-white',
      'Cancelled': 'bg-red-600 text-white',
    }

    return (
      <Badge 
        variant="default"
        className={statusStyles[status] || 'bg-gray-600 text-white'}
      >
        {status}
      </Badge>
    )
  }

  const getPageTitle = () => {
    switch (dateFilter) {
      case 'today': return 'Today\'s Shifts'
      case 'tomorrow': return 'Tomorrow\'s Shifts'
      case 'week': return 'This Week\'s Shifts'
      case 'month': return 'This Month\'s Shifts'
      default: return 'All Shifts'
    }
  }

  const filteredShifts = shifts.filter((shift: any) => {
    if (searchTerm && !shift.jobName?.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !shift.clientName?.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false
    }
    return true
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-white">Loading Shifts...</h1>
                <p className="text-gray-400">Please wait while we load your shifts</p>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="bg-gray-800 border-gray-700">
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-5 w-full bg-gray-700" />
                    <Skeleton className="h-4 w-3/4 bg-gray-700" />
                    <Skeleton className="h-4 w-1/2 bg-gray-700" />
                    <div className="flex justify-between">
                      <Skeleton className="h-5 w-16 bg-gray-700" />
                      <Skeleton className="h-5 w-20 bg-gray-700" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-center min-h-[60vh]">
              <Alert className="max-w-md bg-red-900/20 border-red-800">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-200">
                  Error loading shifts: {error}
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => refetch()}
                    className="mt-2 w-full border-red-700 text-red-200 hover:bg-red-800"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <Header />
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold text-white">{getPageTitle()}</h1>
              <p className="text-gray-400">
                {filteredShifts.length} shift{filteredShifts.length !== 1 ? 's' : ''} found
              </p>
            </div>
            {canManage && (
              <Button onClick={() => router.push('/admin/shifts/new')} className="bg-indigo-600 hover:bg-indigo-700">
                <Plus className="h-4 w-4 mr-2" />
                New Shift
              </Button>
            )}
          </div>

          {/* Filters */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-white flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filters
                </CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    setSearchTerm("")
                    setStatusFilter("all")
                    setDateFilter("all")
                    setClientFilter("all")
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  Clear All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search jobs or clients..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Date Range</label>
                  <Select value={dateFilter} onValueChange={setDateFilter}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="all">All Dates</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="tomorrow">Tomorrow</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="Upcoming">Upcoming</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-300">Company</label>
                  <Select value={companyFilter} onValueChange={setCompanyFilter}>
                    <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700">
                      <SelectItem value="all">All Companies</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shifts Grid */}
          {filteredShifts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <CalendarIcon className="h-12 w-12 text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-gray-300 mb-2">No shifts found</h3>
              <p className="text-gray-500 text-center max-w-md">
                {searchTerm || statusFilter !== 'all' || dateFilter !== 'all' 
                  ? "Try adjusting your filters to see more shifts."
                  : "No shifts have been scheduled yet."}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredShifts.map((shift: any) => (
                <Card
                  key={shift.id}
                  className="bg-gray-800 border-gray-700 hover:bg-gray-750 transition-colors cursor-pointer"
                  onClick={() => handleRowClick(shift.id)}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-white truncate">{shift.jobName}</h3>
                          <p className="text-sm text-gray-400 truncate">{shift.clientName}</p>
                        </div>
                        <div className="flex flex-col gap-2 ml-3">
                          {getDateBadge(shift.date)}
                          {getStatusBadge(shift.status)}
                        </div>
                      </div>

                      {/* Details */}
                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-gray-300">
                          <Clock className="h-4 w-4 mr-2 text-gray-400" />
                          {shift.startTime} - {shift.endTime}
                        </div>
                        {shift.location && (
                          <div className="flex items-center text-sm text-gray-300">
                            <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                            <span className="truncate">{shift.location}</span>
                          </div>
                        )}
                        <div className="flex items-center text-sm text-gray-300">
                          <Users className="h-4 w-4 mr-2 text-gray-400" />
                          {shift.assignedPersonnel?.length || 0} / {shift.requestedWorkers || 0} workers
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
