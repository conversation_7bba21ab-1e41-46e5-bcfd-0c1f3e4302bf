"use client"

import React from 'react';
import { useUser } from '@/hooks/use-user';
import Header from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield,
  Edit,
  Save,
  X
} from 'lucide-react';

export default function ProfilePage() {
  const { user } = useUser();
  const [isEditing, setIsEditing] = React.useState(false);

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-12">
              <p className="text-gray-400">Please log in to view your profile.</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <Header />
      
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Profile Header */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <Avatar className="h-20 w-20 ring-4 ring-indigo-500/20">
                  <AvatarImage 
                    src={user.avatarUrl || user.avatar} 
                    alt={user.name || 'User'} 
                  />
                  <AvatarFallback className="bg-indigo-600 text-white text-xl font-bold">
                    {getInitials(user.name || user.email || 'U')}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                    <div>
                      <CardTitle className="text-2xl text-white">
                        {user.name || 'User'}
                      </CardTitle>
                      <CardDescription className="text-gray-400 mt-1">
                        {user.email}
                      </CardDescription>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="secondary" className="bg-indigo-600/20 text-indigo-300 border-indigo-600/30">
                          <Shield className="h-3 w-3 mr-1" />
                          {user.role || 'Staff'}
                        </Badge>
                      </div>
                    </div>
                    
                    <Button
                      variant={isEditing ? "destructive" : "outline"}
                      size="sm"
                      onClick={() => setIsEditing(!isEditing)}
                      className="shrink-0"
                    >
                      {isEditing ? (
                        <>
                          <X className="h-4 w-4 mr-2" />
                          Cancel
                        </>
                      ) : (
                        <>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit Profile
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Profile Information */}
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Profile Information</CardTitle>
              <CardDescription className="text-gray-400">
                Your personal information and account details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-gray-300">
                    <User className="h-4 w-4 inline mr-2" />
                    Full Name
                  </Label>
                  <Input
                    id="name"
                    value={user.name || ''}
                    disabled={!isEditing}
                    className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-300">
                    <Mail className="h-4 w-4 inline mr-2" />
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={user.email || ''}
                    disabled={!isEditing}
                    className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-gray-300">
                    <Phone className="h-4 w-4 inline mr-2" />
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={user.phone || ''}
                    disabled={!isEditing}
                    placeholder="Not specified"
                    className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="role" className="text-gray-300">
                    <Shield className="h-4 w-4 inline mr-2" />
                    Role
                  </Label>
                  <Input
                    id="role"
                    value={user.role || 'Staff'}
                    disabled
                    className="bg-gray-700 border-gray-600 text-white disabled:opacity-60"
                  />
                </div>
              </div>
              
              <Separator className="bg-gray-700" />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Account Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label className="text-gray-300">
                      <Calendar className="h-4 w-4 inline mr-2" />
                      Member Since
                    </Label>
                    <p className="text-sm text-gray-400 bg-gray-700 p-3 rounded-md">
                      {formatDate(user.createdAt)}
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-gray-300">
                      <Calendar className="h-4 w-4 inline mr-2" />
                      Last Updated
                    </Label>
                    <p className="text-sm text-gray-400 bg-gray-700 p-3 rounded-md">
                      {formatDate(user.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
              
              {isEditing && (
                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsEditing(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      // TODO: Implement save functionality
                      setIsEditing(false);
                    }}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
