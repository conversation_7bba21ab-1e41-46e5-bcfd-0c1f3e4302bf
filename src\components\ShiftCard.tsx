"use client"

import React from 'react';
import { BuildingIcon, CalendarIcon, ClockIcon } from './IconComponents';

type WorkerRole = 'CC' | 'SH' | 'FO' | 'RFO' | 'RG' | 'GL';

export const WORKER_ROLE_DETAILS: Record<WorkerRole, { name: string; colorClass: string }> = {
  'CC': { name: 'Crew Chief', colorClass: 'bg-purple-500' },
  'SH': { name: 'Stage Hand', colorClass: 'bg-blue-500' },
  'FO': { name: 'Fork Operator', colorClass: 'bg-green-500' },
  'RFO': { name: 'Reach Fork Operator', colorClass: 'bg-yellow-500' },
  'RG': { name: '<PERSON>igger', colorClass: 'bg-red-500' },
  'GL': { name: 'General Labor', colorClass: 'bg-gray-500' },
};

interface ShiftCardProps {
  shift: {
    id: string;
    jobName: string;
    companyName: string;
    date: string; 
    startTime: string;
    endTime: string;
    status: string;
    assignments: {
      role: WorkerRole;
      userId: string | null;
    }[];
  };
  onClick: () => void;
}

const ShiftCard: React.FC<ShiftCardProps> = ({ shift, onClick }) => {
  const assignments = shift.assignments || [];
  const totalSlots = assignments.length;
  const filledSlots = assignments.filter(a => a.userId !== null).length;
  const filledPercentage = totalSlots > 0 ? (filledSlots / totalSlots) * 100 : 0;

  // Group assignments by role to get counts
  const roleCounts = assignments.reduce((acc, assignment) => {
    if (WORKER_ROLE_DETAILS[assignment.role]) {
      acc[assignment.role] = (acc[assignment.role] || 0) + 1;
    }
    return acc;
  }, {} as Record<WorkerRole, number>);

  const getStatusClasses = (status: string) => {
    switch (status) {
      case 'In Progress':
        return 'sample-status-in-progress';
      case 'Completed':
        return 'sample-status-completed';
      case 'Upcoming':
      default:
        return 'sample-status-upcoming';
    }
  };

  return (
    <div
      onClick={onClick}
      className="sample-card"
      role="button"
      tabIndex={0}
      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && onClick()}
    >
      <div className="p-6 flex-grow flex flex-col">
        <div className="flex justify-between items-start mb-2">
          <h2 className="text-lg sm:text-xl font-bold text-white leading-tight pr-2">{shift.jobName}</h2>
          <span className={`flex-shrink-0 px-3 py-1 text-xs font-semibold rounded-full ${getStatusClasses(shift.status)}`}>
            {shift.status}
          </span>
        </div>
        
        <div className="flex items-center text-gray-400 mt-2 mb-4 transition-colors group-hover:text-gray-200">
          <BuildingIcon />
          <span className="ml-2 text-sm">{shift.companyName}</span>
        </div>

        <div className="space-y-2 text-gray-300 text-sm border-t border-b border-gray-700 py-3 my-3">
          <div className="flex items-center">
            <CalendarIcon className="h-5 w-5 text-indigo-400" />
            <span className="ml-2 font-medium">
              {new Date(shift.date).toLocaleDateString(undefined, { weekday: 'short', month: 'long', day: 'numeric' })}
            </span>
          </div>
          <div className="flex items-center">
            <ClockIcon className="h-5 w-5 text-indigo-400" />
            <span className="ml-2">{shift.startTime} - {shift.endTime}</span>
          </div>
        </div>
        
        <div className="flex-grow">
          <h3 className="text-xs uppercase font-bold text-gray-500 mb-2">Workers Needed</h3>
          <div className="flex flex-wrap gap-2">
            {Object.entries(roleCounts).map(([role, count]) => {
              const roleDetails = WORKER_ROLE_DETAILS[role as WorkerRole];
              if (!roleDetails) return null;
              return (
                <span key={role} className={`px-2 py-1 text-xs font-semibold text-white rounded-full ${roleDetails.colorClass}`}>
                  {count} {roleDetails.name}
                </span>
              );
            })}
          </div>
        </div>

        <div className="mt-6">
          <div className="flex justify-between mb-1 text-xs text-gray-400">
            <span>Filled Positions</span>
            <span>{filledSlots} of {totalSlots}</span>
          </div>
          <div className="sample-progress-bar">
            <div
              className="sample-progress-fill"
              style={{ width: `${filledPercentage}%` }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShiftCard;
