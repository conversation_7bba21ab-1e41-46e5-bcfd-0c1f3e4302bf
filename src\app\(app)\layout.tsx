import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Hands On Labor AI Dashboard',
  description: 'Workforce management dashboard',
}

export default function AppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className={`${inter.className} bg-gray-900 text-gray-100 min-h-screen`}>
      <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900
        bg-[length:200%_200%] animate-gradient-shift">
        {children}
      </div>
    </div>
  )
}
