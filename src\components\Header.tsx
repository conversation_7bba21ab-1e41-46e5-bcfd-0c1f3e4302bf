import React from 'react';
import Link from 'next/link';
import { Briefcase } from 'lucide-react';
import NavigationBar from './NavigationBar';
import { UserNav } from './user-nav';
import { MobileProfileNav } from './mobile-profile-nav';
import { useUser } from '@/hooks/use-user';

interface HeaderProps {
    children?: React.ReactNode;
    showNavigation?: boolean;
}

const Header: React.FC<HeaderProps> = ({ children, showNavigation = true }) => {
  const { user } = useUser();

  return (
    <header className="sample-header">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center">
              <div className="flex-shrink-0 text-indigo-400">
                 <Briefcase className="h-8 w-8" />
              </div>
              <span className="ml-3 text-xl font-bold text-white hidden sm:block">Hands On Labor</span>
              <span className="ml-3 text-lg font-bold text-white sm:hidden">HOL</span>
            </Link>
          </div>

          <div className="flex items-center gap-3">
            {children && <div>{children}</div>}
            {user && <MobileProfileNav />}
          </div>
        </div>

        {/* Navigation Bar */}
        {showNavigation && user && (
          <NavigationBar />
        )}
      </div>
    </header>
  );
};

export default Header;