'use client';

import { useApi, useMutation } from './use-api';
import { useCallback, useMemo } from 'react';

// Enhanced query hook with standardized patterns
export interface QueryOptions {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
  retryOnMount?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
}

export interface MutationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  onMutate?: () => void;
}

/**
 * Standardized query hook that wraps useApi with consistent patterns
 */
export function useQuery<T>(
  queryKey: string | null,
  dependencies: any[] = [],
  options: QueryOptions = {}
) {
  const result = useApi<T>(queryKey, dependencies, options);
  
  return {
    ...result,
    isLoading: result.loading,
    isError: !!result.error,
    isSuccess: !result.loading && !result.error && result.data !== null,
    isFetching: result.loading,
  };
}

/**
 * Standardized mutation hook
 */
export function useMutationQuery<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: MutationOptions = {}
) {
  const result = useMutation(mutationFn, options);
  
  return {
    ...result,
    mutate: result.mutate,
    mutateAsync: result.mutate,
    isLoading: result.loading,
    isError: !!result.error,
    isSuccess: !result.loading && !result.error,
    isPending: result.loading,
  };
}

// Standardized API hooks for common resources
export const useUsers = (options?: QueryOptions) => 
  useQuery<{ users: any[] }>('/api/users', [], options);

export const useUser = (id: string, options?: QueryOptions) => 
  useQuery<{ user: any }>(`/api/users/${id}`, [id], options);

export const useCompanies = (options?: QueryOptions) => 
  useQuery<{ companies: any[] }>('/api/companies', [], options);

export const useCompany = (id: string, options?: QueryOptions) => 
  useQuery<{ company: any }>(`/api/companies/${id}`, [id], options);

export const useJobs = (options?: QueryOptions) => 
  useQuery<{ jobs: any[] }>('/api/jobs', [], options);

export const useJob = (id: string, options?: QueryOptions) => 
  useQuery<{ job: any }>(`/api/jobs/${id}`, [id], options);

export const useShifts = (options?: QueryOptions) => 
  useQuery<{ shifts: any[] }>('/api/shifts', [], options);

export const useShift = (id: string, options?: QueryOptions) => 
  useQuery<{ shift: any }>(`/api/shifts/${id}`, [id], options);

export const useShiftsByDate = (
  dateFilter: string = 'today',
  statusFilter: string = 'all',
  companyFilter: string = 'all',
  searchTerm: string = '',
  options?: QueryOptions
) => {
  const params = useMemo(() => new URLSearchParams({
    filter: dateFilter,
    status: statusFilter,
    company: companyFilter,
    search: searchTerm,
  }).toString(), [dateFilter, statusFilter, companyFilter, searchTerm]);

  return useQuery<{ shifts: any[], dateRange?: any }>(
    `/api/shifts/by-date?${params}`,
    [dateFilter, statusFilter, companyFilter, searchTerm],
    options
  );
};

export const useTimesheets = (options?: QueryOptions) => 
  useQuery<{ timesheets: any[] }>('/api/timesheets', [], options);

export const useTimesheet = (id: string, options?: QueryOptions) => 
  useQuery<{ timesheet: any }>(`/api/timesheets/${id}`, [id], options);

export const useAnnouncements = (options?: QueryOptions) => 
  useQuery<{ announcements: any[] }>('/api/announcements', [], options);

export const useNotifications = (options?: QueryOptions) => 
  useQuery<{ notifications: any[] }>('/api/notifications', [], options);

export const useWorkerRequirements = (shiftId?: string, options?: QueryOptions) => {
  const url = shiftId ? `/api/worker-requirements?shiftId=${shiftId}` : '/api/worker-requirements';
  return useQuery<{ workerRequirements: any[] }>(url, [shiftId], options);
};

// Dashboard-specific hooks
export const useClientDashboard = (options?: QueryOptions) => 
  useQuery<any>('/api/dashboard/client', [], options);

export const useCrewChiefDashboard = (userId: string, options?: QueryOptions) => 
  useQuery<any>(`/api/crew-chief/${userId}/dashboard`, [userId], options);

export const useManagerDashboard = (options?: QueryOptions) => 
  useQuery<any>('/api/dashboard/manager', [], options);

export const useEmployeeDashboard = (options?: QueryOptions) => 
  useQuery<any>('/api/dashboard/employee', [], options);

// Mutation hooks for common operations
export const useCreateUser = (options?: MutationOptions) => 
  useMutationQuery(
    (userData: any) => fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData),
    }).then(res => res.json()),
    options
  );

export const useUpdateUser = (options?: MutationOptions) => 
  useMutationQuery(
    ({ id, ...userData }: { id: string } & any) => fetch(`/api/users/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(userData),
    }).then(res => res.json()),
    options
  );

export const useDeleteUser = (options?: MutationOptions) => 
  useMutationQuery(
    (id: string) => fetch(`/api/users/${id}`, {
      method: 'DELETE',
    }).then(res => res.json()),
    options
  );

export const useCreateJob = (options?: MutationOptions) => 
  useMutationQuery(
    (jobData: any) => fetch('/api/jobs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(jobData),
    }).then(res => res.json()),
    options
  );

export const useUpdateJob = (options?: MutationOptions) => 
  useMutationQuery(
    ({ id, ...jobData }: { id: string } & any) => fetch(`/api/jobs/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(jobData),
    }).then(res => res.json()),
    options
  );

export const useCreateShift = (options?: MutationOptions) => 
  useMutationQuery(
    (shiftData: any) => fetch('/api/shifts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(shiftData),
    }).then(res => res.json()),
    options
  );

export const useClockIn = (options?: MutationOptions) => 
  useMutationQuery(
    ({ shiftId, ...clockInData }: { shiftId: string } & any) => fetch(`/api/shifts/${shiftId}/clock-in`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(clockInData),
    }).then(res => res.json()),
    options
  );

export const useClockOut = (options?: MutationOptions) => 
  useMutationQuery(
    ({ shiftId, ...clockOutData }: { shiftId: string } & any) => fetch(`/api/shifts/${shiftId}/clock-out`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(clockOutData),
    }).then(res => res.json()),
    options
  );

export const useApproveTimesheet = (options?: MutationOptions) => 
  useMutationQuery(
    ({ timesheetId, ...approvalData }: { timesheetId: string } & any) => fetch(`/api/timesheets/${timesheetId}/approve`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(approvalData),
    }).then(res => res.json()),
    options
  );

export const useRejectTimesheet = (options?: MutationOptions) => 
  useMutationQuery(
    ({ timesheetId, ...rejectionData }: { timesheetId: string } & any) => fetch(`/api/timesheets/${timesheetId}/reject`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(rejectionData),
    }).then(res => res.json()),
    options
  );

export const useCreateAnnouncement = (options?: MutationOptions) => 
  useMutationQuery(
    (announcementData: any) => fetch('/api/announcements', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(announcementData),
    }).then(res => res.json()),
    options
  );

export const useMarkNotificationRead = (options?: MutationOptions) => 
  useMutationQuery(
    (notificationId: string) => fetch(`/api/notifications/${notificationId}/read`, {
      method: 'POST',
    }).then(res => res.json()),
    options
  );

export const useBulkNotificationAction = (options?: MutationOptions) => 
  useMutationQuery(
    (actionData: { action: string; notificationIds: string[] }) => fetch('/api/notifications/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(actionData),
    }).then(res => res.json()),
    options
  );

// Utility hooks for common patterns
export const useInfiniteQuery = <T>(
  baseUrl: string,
  dependencies: any[] = [],
  options: QueryOptions & { pageSize?: number } = {}
) => {
  const { pageSize = 20 } = options;
  // This would need to be implemented with pagination logic
  // For now, return the standard query
  return useQuery<T>(baseUrl, dependencies, options);
};

export const usePaginatedQuery = <T>(
  baseUrl: string,
  page: number = 1,
  limit: number = 20,
  dependencies: any[] = [],
  options: QueryOptions = {}
) => {
  const url = `${baseUrl}?limit=${limit}&offset=${(page - 1) * limit}`;
  return useQuery<T>(url, [page, limit, ...dependencies], options);
};
