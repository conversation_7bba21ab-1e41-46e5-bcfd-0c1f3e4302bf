import { NextRequest } from 'next/server';
import { type User, UserRole } from './types';
import { getServerSession } from 'next-auth';
import { authOptions } from './auth-config';
import jwt from 'jsonwebtoken';

// Helper function to get user from request (for use in API routes)
export async function getCurrentUser(req: NextRequest): Promise<User | null> {
  const authHeader = req.headers.get('authorization');
  
  // Handle JWT token from header
  if (authHeader?.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as {
        id: string;
        email: string;
        name: string;
        role: UserRole;
        companyId?: string;
        avatarUrl?: string;
      };
      return {
        id: decoded.id,
        email: decoded.email,
        name: decoded.name,
        role: decoded.role,
        companyId: decoded.companyId || null,
        avatarUrl: decoded.avatarUrl
      };
    } catch (error) {
      console.error('Token verification failed:', error);
    }
  }

  // Fallback to session
  const session = await getServerSession(authOptions);
  if (session?.user) {
    return {
      id: session.user.id,
      email: session.user.email!,
      name: session.user.name!,
      role: session.user.role,
      companyId: session.user.companyId || null,
      avatarUrl: session.user.image || `https://i.pravatar.cc/32?u=${session.user.email}`
    };
  }

  return null;
}
