const { PrismaClient } = require('@prisma/client');

// Cloud SQL uses Google's managed SSL certificates
console.log('✅ Using Google Cloud SQL with managed SSL certificates');

const prisma = new PrismaClient({
  log: ['query', 'error', 'warn'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
});

async function testConnection() {
  try {
    await prisma.$connect();
    console.log('✅ Successfully connected to database');
    const result = await prisma.$queryRaw`SELECT 1`;
    console.log('Test query result:', result);
  } catch (error) {
    console.error('❌ Database connection error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
