#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { Client } from 'pg';
import * as net from 'net';
import * as dns from 'dns';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

const dnsLookup = promisify(dns.lookup);

// Cloud SQL uses Google's managed SSL certificates
console.log('✅ Using Google Cloud SQL with managed SSL certificates');

// Database configuration from environment
const DATABASE_URL = process.env.DATABASE_URL || '';
const HOST = 'pg-3595fcb-hol619.b.aivencloud.com';
const PORT = 12297;

async function runDatabaseTests() {
console.log('🔍 Starting comprehensive database connectivity test...\n');

// Test 1: Environment Variables
console.log('1️⃣ Testing Environment Variables:');
console.log(`DATABASE_URL exists: ${!!DATABASE_URL}`);
console.log(`DATABASE_URL format: ${DATABASE_URL ? 'Valid' : 'Invalid'}`);
if (DATABASE_URL) {
  const url = new URL(DATABASE_URL);
  console.log(`  - Protocol: ${url.protocol}`);
  console.log(`  - Host: ${url.hostname}`);
  console.log(`  - Port: ${url.port}`);
  console.log(`  - Database: ${url.pathname.slice(1)}`);
  console.log(`  - SSL Mode: ${url.searchParams.get('sslmode')}`);
}
console.log('');

// Test 2: DNS Resolution
console.log('2️⃣ Testing DNS Resolution:');
try {
  const address = await dnsLookup(HOST);
  console.log(`✅ DNS Resolution successful: ${HOST} -> ${address.address}`);
} catch (error) {
  console.log(`❌ DNS Resolution failed: ${error}`);
}
console.log('');

// Test 3: Network Connectivity
console.log('3️⃣ Testing Network Connectivity:');
const testConnection = () => {
  return new Promise<boolean>((resolve) => {
    const socket = new net.Socket();
    const timeout = 10000; // 10 seconds

    socket.setTimeout(timeout);
    
    socket.on('connect', () => {
      console.log(`✅ Network connection successful to ${HOST}:${PORT}`);
      socket.destroy();
      resolve(true);
    });

    socket.on('timeout', () => {
      console.log(`❌ Network connection timeout after ${timeout}ms`);
      socket.destroy();
      resolve(false);
    });

    socket.on('error', (error) => {
      console.log(`❌ Network connection error: ${error.message}`);
      socket.destroy();
      resolve(false);
    });

    socket.connect(PORT, HOST);
  });
};

const networkConnected = await testConnection();
console.log('');

// Test 4: Raw PostgreSQL Connection
console.log('4️⃣ Testing Raw PostgreSQL Connection:');
try {
  // Try using environment variable approach (same as Prisma)
  console.log('Attempting connection using environment variable SSL configuration...');
  const client = new Client({
    connectionString: DATABASE_URL,
    connectionTimeoutMillis: 10000,
    query_timeout: 10000,
    // Let the client use the PGSSLROOTCERT environment variable we set earlier
  });

  await client.connect();
  console.log('✅ Raw PostgreSQL connection successful using environment SSL config');

  // Test a simple query
  const result = await client.query('SELECT version()');
  console.log(`✅ Query successful: ${result.rows[0].version.substring(0, 50)}...`);

  await client.end();
} catch (error: any) {
  console.log(`❌ Environment SSL connection failed: ${error.message}`);

  // Try with explicit SSL configuration
  try {
    console.log('Attempting connection with explicit SSL configuration...');
    const caCert = fs.readFileSync(caCertPath, 'utf8');
    const clientExplicit = new Client({
      connectionString: DATABASE_URL,
      connectionTimeoutMillis: 10000,
      query_timeout: 10000,
      ssl: {
        rejectUnauthorized: true,
        ca: caCert,
        checkServerIdentity: () => undefined, // Skip hostname verification
      },
    });

    await clientExplicit.connect();
    console.log('✅ Raw PostgreSQL connection successful with explicit SSL');

    // Test a simple query
    const result = await clientExplicit.query('SELECT version()');
    console.log(`✅ Query successful: ${result.rows[0].version.substring(0, 50)}...`);

    await clientExplicit.end();
  } catch (explicitError: any) {
    console.log(`❌ Explicit SSL connection also failed: ${explicitError.message}`);
    console.log(`Error code: ${explicitError.code}`);
    console.log('ℹ️ Note: Raw pg client SSL issues are common with Aiven. Prisma handles this better.');
  }
}
console.log('');

// Test 5: Prisma Connection
console.log('5️⃣ Testing Prisma Connection:');
try {
  const prisma = new PrismaClient({
    log: ['error', 'warn'],
    datasources: {
      db: {
        url: DATABASE_URL
      }
    }
  });

  console.log('Attempting Prisma connection...');
  await prisma.$connect();
  console.log('✅ Prisma connection successful');

  // Test a simple query
  const result = await prisma.$queryRaw`SELECT 1 as test`;
  console.log(`✅ Prisma query successful: ${JSON.stringify(result)}`);

  // Test table access
  try {
    const userCount = await prisma.user.count();
    console.log(`✅ User table accessible, count: ${userCount}`);
  } catch (error: any) {
    console.log(`⚠️ User table access failed: ${error.message}`);
  }

  await prisma.$disconnect();
} catch (error: any) {
  console.log(`❌ Prisma connection failed: ${error.message}`);
  console.log(`Error code: ${error.code}`);
  console.log(`Error name: ${error.name}`);
  if (error.meta) {
    console.log(`Error meta: ${JSON.stringify(error.meta, null, 2)}`);
  }
}
console.log('');

// Test 6: Connection Pool Test
console.log('6️⃣ Testing Connection Pool:');
try {
  const prisma = new PrismaClient({
    log: ['error'],
    datasources: {
      db: {
        url: DATABASE_URL + '&connection_limit=5&pool_timeout=10'
      }
    }
  });

  const promises = Array.from({ length: 3 }, async (_, i) => {
    try {
      const result = await prisma.$queryRaw`SELECT ${i + 1} as connection_test`;
      return `Connection ${i + 1}: ✅`;
    } catch (error: any) {
      return `Connection ${i + 1}: ❌ ${error.message}`;
    }
  });

  const results = await Promise.all(promises);
  results.forEach(result => console.log(result));

  await prisma.$disconnect();
} catch (error: any) {
  console.log(`❌ Connection pool test failed: ${error.message}`);
}
console.log('');

console.log('🏁 Database connectivity test completed!');
console.log('\n📋 Summary:');
console.log(`- Environment: ${!!DATABASE_URL ? '✅' : '❌'}`);
console.log(`- DNS: ${await dnsLookup(HOST).then(() => '✅').catch(() => '❌')}`);
console.log(`- Network: ${networkConnected ? '✅' : '❌'}`);
console.log('\n💡 Next steps:');
if (!networkConnected) {
  console.log('- Check firewall rules and network connectivity');
  console.log('- Verify the database server is running');
  console.log('- Check if your IP is whitelisted in Aiven console');
}
console.log('- Review Aiven database status in their console');
console.log('- Check database credentials and connection string');
}

// Run the tests
runDatabaseTests().catch(console.error);
