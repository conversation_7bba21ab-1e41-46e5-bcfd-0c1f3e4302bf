"use client"

import React, { useState } from "react"
import { Card, Table, Button, Badge, Checkbox, Select, Avatar, TextInput, Textarea, Modal, Group, Text, Stack, Title, ActionIcon, NumberInput } from "@mantine/core"
import { Users, UserCheck, Settings, Save, X, Edit, Plus, UserPlus, Eye, EyeOff } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useApi } from "@/hooks/use-api"
import type { User, UserRole } from "@/lib/types"
import { notifications } from "@mantine/notifications"
import Header from '@/components/Header'

interface EmployeeManagementProps {}

function EmployeesPage({}: EmployeeManagementProps) {
  const [editingUser, setEditingUser] = useState<string | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [generatedPassword, setGeneratedPassword] = useState('')

  const { data: usersData, loading: usersLoading, refetch: refetchUsers } = useApi<{ users: User[] }>('/api/users')
  const users = usersData?.users || []

  const [editForm, setEditForm] = useState<Partial<User>>({})
  const [newUserForm, setNewUserForm] = useState({
    name: '',
    email: '',
    password: '',
    role: 'Employee' as UserRole,
    location: '',
    performance: 0,
    crewChiefEligible: false,
    forkOperatorEligible: false,
    certifications: [] as string[],
    companyName: '',
    companyAddress: '',
    contactPerson: '',
    contactEmail: '',
    contactPhone: ''
  })

  const startEditing = (user: User) => {
    setEditingUser(user.id)
    setEditForm({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      crewChiefEligible: user.crewChiefEligible || false,
      forkOperatorEligible: user.forkOperatorEligible || false,
      location: user.location || '',
      certifications: user.certifications || [],
      performance: user.performance || 0,
      companyName: user.companyName || '',
      contactPerson: user.contactPerson || '',
      contactEmail: user.contactEmail || '',
      contactPhone: user.contactPhone || ''
    })
  }

  const cancelEditing = () => {
    setEditingUser(null)
    setEditForm({})
  }

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    setGeneratedPassword(password)
    setNewUserForm({ ...newUserForm, password })
    return password
  }

  const resetNewUserForm = () => {
    setNewUserForm({
      name: '',
      email: '',
      password: '',
      role: 'Employee' as UserRole,
      location: '',
      performance: 0,
      crewChiefEligible: false,
      forkOperatorEligible: false,
      certifications: [],
      companyName: '',
      companyAddress: '',
      contactPerson: '',
      contactEmail: '',
      contactPhone: ''
    })
    setGeneratedPassword('')
    setShowPassword(false)
  }

  const openCreateDialog = () => {
    resetNewUserForm()
    generatePassword()
    setShowCreateDialog(true)
  }

  const saveUser = async () => {
    if (!editForm.id) return

    setIsUpdating(true)
    try {
      // Only send fields that are expected by the validation schema
      const updateData = {
        name: editForm.name,
        email: editForm.email,
        role: editForm.role,
        location: editForm.location,
        certifications: editForm.certifications,
        performance: editForm.performance,
        crewChiefEligible: editForm.crewChiefEligible,
        forkOperatorEligible: editForm.forkOperatorEligible,
      }

      const response = await fetch(`/api/users/${editForm.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update user')
      }

      notifications.show({
        title: "User Updated",
        message: `${editForm.name}'s information has been updated successfully`,
        color: 'green'
      })

      setEditingUser(null)
      setEditForm({})
      await refetchUsers()
    } catch (error) {
      notifications.show({
        title: "Error",
        message: error instanceof Error ? error.message : "Failed to update user",
        color: 'red'
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const createUser = async () => {
    if (!newUserForm.name || !newUserForm.email || !newUserForm.password) {
      notifications.show({
        title: "Validation Error",
        message: "Name, email, and password are required",
        color: 'red'
      })
      return
    }

    setIsCreating(true)
    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUserForm)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create user')
      }

      notifications.show({
        title: "User Created",
        message: `${newUserForm.name} has been created successfully`,
        color: 'green'
      })

      setShowCreateDialog(false)
      resetNewUserForm()
      refetchUsers()
    } catch (error) {
      notifications.show({
        title: "Error",
        message: error instanceof Error ? error.message : "Failed to create user",
        color: 'red'
      })
    } finally {
      setIsCreating(false)
    }
  }

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case 'Admin': return 'purple'
      case 'Crew Chief': return 'blue'
      case 'Employee': return 'green'
      case 'Client': return 'orange'
      default: return 'gray'
    }
  }

  const getEligibilityDisplay = (user: User) => {
    const eligibilities = []
    if (user.crewChiefEligible) eligibilities.push('CC')
    if (user.forkOperatorEligible) eligibilities.push('FO')
    return eligibilities.length > 0 ? eligibilities.join(', ') : 'None'
  }

  if (usersLoading) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <Header />
        <main className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <Group justify="center" style={{ height: '64vh' }}>
              <Text size="lg">Loading employees...</Text>
            </Group>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <Header>
        <Button onClick={openCreateDialog} className="sample-btn-primary">
          <UserPlus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </Header>

      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <Stack gap="lg">
        <Group justify="space-between">
          <div>
            <Title order={1}>Employee Management</Title>
            <Text c="dimmed">
              Manage user roles, permissions, and employee eligibility settings
            </Text>
          </div>
          <Group>
            <Badge variant="light" leftSection={<Users size={14} />}>
              {users.length} Total Users
            </Badge>
            <Button onClick={openCreateDialog} leftSection={<UserPlus size={16} />}>
              Add User
            </Button>
          </Group>
        </Group>

        <Group>
          <Card withBorder p="md" radius="md" style={{ flex: 1 }}>
            <Group>
              <Users size={24} />
              <Text>Employees</Text>
            </Group>
            <Text size="xl" fw={700}>{users.filter(u => u.role === 'Employee').length}</Text>
          </Card>
          <Card withBorder p="md" radius="md" style={{ flex: 1 }}>
            <Group>
              <UserCheck size={24} />
              <Text>Crew Chiefs</Text>
            </Group>
            <Text size="xl" fw={700}>{users.filter(u => u.role === 'Crew Chief').length}</Text>
          </Card>
          <Card withBorder p="md" radius="md" style={{ flex: 1 }}>
            <Group>
              <Settings size={24} />
              <Text>CC Eligible</Text>
            </Group>
            <Text size="xl" fw={700}>{users.filter(u => u.crewChiefEligible).length}</Text>
          </Card>
          <Card withBorder p="md" radius="md" style={{ flex: 1 }}>
            <Group>
              <Settings size={24} />
              <Text>FO Eligible</Text>
            </Group>
            <Text size="xl" fw={700}>{users.filter(u => u.forkOperatorEligible).length}</Text>
          </Card>
        </Group>

        <Card withBorder radius="md">
          <Card.Section withBorder inheritPadding py="xs">
            <Group>
              <Users size={20} />
              <Title order={4}>All Users</Title>
            </Group>
            <Text size="sm" c="dimmed">
              Manage user roles and permissions. Click the edit button to modify user settings.
            </Text>
          </Card.Section>
          <Card.Section>
            <Table>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>User</Table.Th>
                  <Table.Th>Role</Table.Th>
                  <Table.Th>Eligibility</Table.Th>
                  <Table.Th>Location</Table.Th>
                  <Table.Th>Performance</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {users.map((user) => (
                  <Table.Tr key={user.id}>
                    <Table.Td>
                      <Group>
                        <Avatar src={user.avatar} radius="xl">
                          {user.name.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                        <div>
                          <Text fw={500}>{user.name}</Text>
                          <Text size="sm" c="dimmed">{user.email}</Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      {editingUser === user.id ? (
                        <Select
                          value={editForm.role}
                          onChange={(value) => setEditForm({ ...editForm, role: value as UserRole })}
                          data={['Employee', 'Crew Chief', 'Admin', 'Client']}
                        />
                      ) : (
                        <Badge color={getRoleBadgeColor(user.role)}>
                          {user.role}
                        </Badge>
                      )}
                    </Table.Td>
                    <Table.Td>
                      {editingUser === user.id ? (
                        <Stack>
                          <Checkbox
                            label="Crew Chief"
                            checked={editForm.crewChiefEligible}
                            onChange={(event) => 
                              setEditForm({ ...editForm, crewChiefEligible: event.currentTarget.checked })
                            }
                          />
                          <Checkbox
                            label="Fork Operator"
                            checked={editForm.forkOperatorEligible}
                            onChange={(event) => 
                              setEditForm({ ...editForm, forkOperatorEligible: event.currentTarget.checked })
                            }
                          />
                        </Stack>
                      ) : (
                        <Text size="sm">{getEligibilityDisplay(user)}</Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      {editingUser === user.id ? (
                        <TextInput
                          value={editForm.location || ''}
                          onChange={(event) => setEditForm({ ...editForm, location: event.currentTarget.value })}
                          placeholder="Location"
                        />
                      ) : (
                        <Text size="sm">{user.location || '-'}</Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      {editingUser === user.id ? (
                        <NumberInput
                          min={0}
                          max={5}
                          step={0.1}
                          value={editForm.performance || 0}
                          onChange={(value) => setEditForm({ ...editForm, performance: Number(value) || 0 })}
                        />
                      ) : (
                        <Text size="sm">
                          {user.performance ? `${user.performance}/5` : '-'}
                        </Text>
                      )}
                    </Table.Td>
                    <Table.Td>
                      {editingUser === user.id ? (
                        <Group>
                          <ActionIcon onClick={saveUser} loading={isUpdating} variant="filled" color="blue"><Save size={16} /></ActionIcon>
                          <ActionIcon onClick={cancelEditing} disabled={isUpdating} variant="outline"><X size={16} /></ActionIcon>
                        </Group>
                      ) : (
                        <ActionIcon onClick={() => startEditing(user)} variant="outline"><Edit size={16} /></ActionIcon>
                      )}
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Card.Section>
        </Card>

        <Modal opened={showCreateDialog} onClose={() => setShowCreateDialog(false)} title="Create New User" size="lg">
          <Stack>
            <Title order={4}>Basic Information</Title>
            <TextInput
              label="Full Name"
              value={newUserForm.name}
              onChange={(event) => setNewUserForm({ ...newUserForm, name: event.currentTarget.value })}
              placeholder="Enter full name"
              required
            />
            <TextInput
              label="Email Address"
              type="email"
              value={newUserForm.email}
              onChange={(event) => setNewUserForm({ ...newUserForm, email: event.currentTarget.value })}
              placeholder="Enter email address"
              required
            />
            <Group>
              <TextInput
                label="Password"
                type={showPassword ? "text" : "password"}
                value={newUserForm.password}
                onChange={(event) => setNewUserForm({ ...newUserForm, password: event.currentTarget.value })}
                placeholder="Enter password"
                required
                style={{ flex: 1 }}
              />
              <Button onClick={() => setShowPassword(!showPassword)} variant="subtle" size="xs" style={{ alignSelf: 'flex-end' }}>
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </Button>
              <Button onClick={generatePassword} variant="outline" style={{ alignSelf: 'flex-end' }}>
                Generate
              </Button>
            </Group>
            {generatedPassword && (
              <Text size="sm" c="dimmed">
                Generated password: <code>{generatedPassword}</code>
              </Text>
            )}
            <Select
              label="Role"
              value={newUserForm.role}
              onChange={(value) => setNewUserForm({ ...newUserForm, role: value as UserRole })}
              data={['Employee', 'Crew Chief', 'Admin', 'Client']}
              required
            />

            {(newUserForm.role === 'Employee' || newUserForm.role === 'Crew Chief' || newUserForm.role === 'Admin') && (
              <>
                <Title order={4}>Employee Information</Title>
                <TextInput
                  label="Location"
                  value={newUserForm.location}
                  onChange={(event) => setNewUserForm({ ...newUserForm, location: event.currentTarget.value })}
                  placeholder="Work location"
                />
                <NumberInput
                  label="Performance Rating (0-5)"
                  min={0}
                  max={5}
                  step={0.1}
                  value={newUserForm.performance}
                  onChange={(value) => setNewUserForm({ ...newUserForm, performance: Number(value) || 0 })}
                />
                <Checkbox
                  label="Crew Chief Eligible"
                  checked={newUserForm.crewChiefEligible}
                  onChange={(event) =>
                    setNewUserForm({ ...newUserForm, crewChiefEligible: event.currentTarget.checked })
                  }
                />
                <Checkbox
                  label="Fork Operator Eligible"
                  checked={newUserForm.forkOperatorEligible}
                  onChange={(event) =>
                    setNewUserForm({ ...newUserForm, forkOperatorEligible: event.currentTarget.checked })
                  }
                />
              </>
            )}

            {newUserForm.role === 'Client' && (
              <>
                <Title order={4}>Company Information</Title>
                <TextInput
                  label="Company Name"
                  value={newUserForm.companyName}
                  onChange={(event) => setNewUserForm({ ...newUserForm, companyName: event.currentTarget.value })}
                  placeholder="Company name"
                />
                <Textarea
                  label="Company Address"
                  value={newUserForm.companyAddress}
                  onChange={(event) => setNewUserForm({ ...newUserForm, companyAddress: event.currentTarget.value })}
                  placeholder="Company address"
                />
                <TextInput
                  label="Contact Person"
                  value={newUserForm.contactPerson}
                  onChange={(event) => setNewUserForm({ ...newUserForm, contactPerson: event.currentTarget.value })}
                  placeholder="Primary contact person"
                />
                <TextInput
                  label="Contact Email"
                  type="email"
                  value={newUserForm.contactEmail}
                  onChange={(event) => setNewUserForm({ ...newUserForm, contactEmail: event.currentTarget.value })}
                  placeholder="Contact email"
                />
                <TextInput
                  label="Contact Phone"
                  value={newUserForm.contactPhone}
                  onChange={(event) => setNewUserForm({ ...newUserForm, contactPhone: event.currentTarget.value })}
                  placeholder="Contact phone number"
                />
              </>
            )}

            <Group justify="flex-end">
              <Button variant="default" onClick={() => setShowCreateDialog(false)} disabled={isCreating}>
                Cancel
              </Button>
              <Button
                onClick={createUser}
                loading={isCreating}
                disabled={!newUserForm.name || !newUserForm.email || !newUserForm.password}
              >
                Create User
              </Button>
            </Group>
          </Stack>
        </Modal>
          </Stack>
        </div>
      </main>
    </div>
  )
}

export default EmployeesPage;
